"""
日志管理模块
支持中文日志输出和按日期命名的日志文件
"""

import logging
import os
from datetime import datetime
from pathlib import Path


class ECMWFLogger:
    """ECMWF 数据下载日志管理器"""
    
    def __init__(self, log_dir: str = "./log"):
        """
        初始化日志管理器
        
        Args:
            log_dir: 日志目录路径
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 生成日志文件名 - 使用当前日期
        today = datetime.now().strftime("%Y%m%d")
        self.log_file = self.log_dir / f"ecmwf_{today}.log"
        
        # 配置日志
        self._setup_logger()
    
    def _setup_logger(self):
        """配置日志格式和输出"""
        # 创建日志记录器
        self.logger = logging.getLogger('ecmwf_downloader')
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(
                self.log_file, 
                mode='a', 
                encoding='utf-8'
            )
            file_handler.setLevel(logging.INFO)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def info(self, message: str):
        """输出信息级别日志"""
        self.logger.info(message)
    
    def error(self, message: str):
        """输出错误级别日志"""
        self.logger.error(message)
    
    def warning(self, message: str):
        """输出警告级别日志"""
        self.logger.warning(message)
    
    def debug(self, message: str):
        """输出调试级别日志"""
        self.logger.debug(message)


# 创建全局日志实例
logger = ECMWFLogger() 