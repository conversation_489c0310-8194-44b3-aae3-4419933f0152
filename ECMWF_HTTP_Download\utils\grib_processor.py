"""
GRIB2数据处理模块
使用cfgrib和xarray处理GRIB2文件并转换为NetCDF格式
"""

import xarray as xr
import cfgrib
from typing import Dict, List, Optional
from pathlib import Path
import tempfile

from .logger import logger


class GribProcessor:
    """GRIB2文件处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.datasets = {}
    
    def load_grib_data(self, file_path: str, 
                      param_code: str) -> Optional[xr.Dataset]:
        """
        加载GRIB2文件数据
        
        Args:
            file_path: GRIB2文件路径
            param_code: 参数代码
            
        Returns:
            xarray Dataset对象
        """
        try:
            logger.info(f"开始加载GRIB2文件：{file_path}")
            
            # 使用cfgrib加载GRIB2文件
            # 指定filter_by_keys来筛选特定参数
            dataset = cfgrib.open_dataset(
                file_path,
                filter_by_keys={'shortName': param_code}
            )
            
            logger.info(f"成功加载GRIB2数据，变量：{list(dataset.data_vars.keys())}")
            return dataset
            
        except Exception as e:
            logger.error(f"加载GRIB2文件失败：{e}")
            return None
    
    def process_single_step(self, grib_data: bytes, step: int,
                           param_code: str) -> Optional[xr.Dataset]:
        """
        处理单个时间步长的GRIB2数据
        
        Args:
            grib_data: GRIB2二进制数据
            step: 时间步长
            param_code: 参数代码
            
        Returns:
            xarray Dataset对象
        """
        temp_file = None
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(
                mode='wb', 
                suffix='.grib2', 
                delete=False
            ) as tmp_file:
                tmp_file.write(grib_data)
                temp_file = tmp_file.name
            
            # 加载数据
            dataset = self.load_grib_data(temp_file, param_code)
            
            if dataset is not None:
                # 先将数据加载到内存中，避免依赖临时文件
                dataset = dataset.load()
                # 添加时间步长信息
                dataset = dataset.assign_coords(step=step)
                logger.info(f"成功处理步长 {step} 的数据")
                
            return dataset
            
        except Exception as e:
            logger.error(f"处理步长 {step} 的数据失败：{e}")
            return None
            
        finally:
            # 清理临时文件
            if temp_file:
                try:
                    Path(temp_file).unlink(missing_ok=True)
                except Exception as e:
                    logger.warning(f"清理临时文件失败：{e}")
    
    def process_multiple_steps(self, grib_data_dict: Dict[int, bytes],
                              param_code: str) -> Dict[int, xr.Dataset]:
        """
        处理多个时间步长的GRIB2数据
        
        Args:
            grib_data_dict: 字典，键为step，值为GRIB2二进制数据
            param_code: 参数代码
            
        Returns:
            字典，键为step，值为对应的xarray Dataset
        """
        results = {}
        
        for step, grib_data in grib_data_dict.items():
            dataset = self.process_single_step(grib_data, step, param_code)
            if dataset is not None:
                results[step] = dataset
        
        logger.info(f"成功处理 {len(results)} 个时间步长的数据")
        return results
    
    def merge_datasets(self, datasets_dict: Dict[int, xr.Dataset],
                      param_code: str) -> Optional[xr.Dataset]:
        """
        合并多个时间步长的数据集
        
        Args:
            datasets_dict: 字典，键为step，值为xarray Dataset
            param_code: 参数代码
            
        Returns:
            合并后的xarray Dataset
        """
        try:
            if not datasets_dict:
                logger.error("没有可合并的数据集")
                return None
            
            # 按step排序
            sorted_datasets = [
                datasets_dict[step] for step in sorted(datasets_dict.keys())
            ]
            
            logger.info(f"开始合并 {len(sorted_datasets)} 个数据集")
            
            # 沿着step维度合并
            merged_dataset = xr.concat(
                sorted_datasets, 
                dim='step',
                coords='minimal',
                compat='override'
            )
            
            # 添加时间维度属性
            merged_dataset = merged_dataset.assign_attrs({
                'description': f'{param_code} data from ECMWF',
                'source': 'ECMWF AIFS',
                'parameter': param_code,
                'steps': sorted(datasets_dict.keys())
            })
            
            logger.info(f"成功合并数据集，形状：{merged_dataset.dims}")
            return merged_dataset
            
        except Exception as e:
            logger.error(f"合并数据集失败：{e}")
            return None
    
    def save_to_netcdf(self, dataset: xr.Dataset, 
                      output_path: str,
                      param_code: str) -> bool:
        """
        保存数据集为NetCDF文件
        
        Args:
            dataset: xarray Dataset
            output_path: 输出文件路径
            param_code: 参数代码
            
        Returns:
            保存是否成功
        """
        try:
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"开始保存NetCDF文件：{output_path}")
            
            # 保存为NetCDF4格式
            dataset.to_netcdf(
                output_path,
                format='NETCDF4',
                engine='netcdf4'
            )
            
            file_size = Path(output_path).stat().st_size
            logger.info(f"成功保存NetCDF文件，大小：{file_size / 1024 / 1024:.2f} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"保存NetCDF文件失败：{e}")
            return False
    
    def get_data_info(self, dataset: xr.Dataset) -> Dict:
        """
        获取数据集信息
        
        Args:
            dataset: xarray Dataset
            
        Returns:
            包含数据集信息的字典
        """
        try:
            info = {
                'variables': list(dataset.data_vars.keys()),
                'dimensions': dict(dataset.dims),
                'coordinates': list(dataset.coords.keys()),
                'shape': dataset.sizes,
                'attrs': dict(dataset.attrs)
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据集信息失败：{e}")
            return {} 