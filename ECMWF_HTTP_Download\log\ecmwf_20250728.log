2025-07-28 11:34:02 - INFO - 成功加载配置文件：config\01_total_cloud_cover.yaml
2025-07-28 11:34:02 - INFO - 成功加载配置文件：config\02_wind_u10.yaml
2025-07-28 11:34:02 - INFO - 成功加载配置文件：config\03_wind_v10.yaml
2025-07-28 11:34:02 - INFO - 成功加载配置文件：config\04_mean_sea_level.yaml
2025-07-28 11:34:02 - INFO - 成功加载配置文件：config\05_total_precipitation.yaml
2025-07-28 11:34:58 - INFO - 成功加载配置文件：config\01_total_cloud_cover.yaml
2025-07-28 11:34:58 - INFO - 成功加载配置文件：config\02_wind_u10.yaml
2025-07-28 11:34:58 - INFO - 成功加载配置文件：config\03_wind_v10.yaml
2025-07-28 11:34:58 - INFO - 成功加载配置文件：config\04_mean_sea_level.yaml
2025-07-28 11:34:58 - INFO - 成功加载配置文件：config\05_total_precipitation.yaml
2025-07-28 11:34:58 - INFO - 所有配置文件验证通过
2025-07-28 11:34:58 - INFO - 步长 24h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-24h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 24h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-24h-oper-fc.index
2025-07-28 11:34:58 - INFO - 步长 48h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-48h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 48h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-48h-oper-fc.index
2025-07-28 11:34:58 - INFO - 步长 72h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-72h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 72h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-72h-oper-fc.index
2025-07-28 11:34:58 - INFO - 步长 96h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-96h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 96h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-96h-oper-fc.index
2025-07-28 11:34:58 - INFO - 步长 120h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-120h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 120h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-120h-oper-fc.index
2025-07-28 11:34:58 - INFO - 步长 144h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-144h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 144h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-144h-oper-fc.index
2025-07-28 11:34:58 - INFO - 步长 168h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-168h-oper-fc.grib2
2025-07-28 11:34:58 - INFO - 步长 168h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-168h-oper-fc.index
2025-07-28 11:35:52 - INFO - ============================================================
2025-07-28 11:35:52 - INFO - 步骤 1：扫描./config目录并更新配置文件到当前UTC时间
2025-07-28 11:35:52 - INFO - ============================================================
2025-07-28 11:35:52 - INFO - 找到 5 个配置文件
2025-07-28 11:35:52 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 11:35:52 - INFO - ✓ 已更新：01_total_cloud_cover.yaml
2025-07-28 11:35:52 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 11:35:52 - INFO - ✓ 已更新：02_wind_u10.yaml
2025-07-28 11:35:52 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 11:35:52 - INFO - ✓ 已更新：03_wind_v10.yaml
2025-07-28 11:35:52 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 11:35:52 - INFO - ✓ 已更新：04_mean_sea_level.yaml
2025-07-28 11:35:52 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 11:35:52 - INFO - ✓ 已更新：05_total_precipitation.yaml
2025-07-28 11:35:52 - INFO - ============================================================
2025-07-28 11:35:52 - INFO - 步骤 2：测试批量处理器设置
2025-07-28 11:35:52 - INFO - ============================================================
2025-07-28 11:35:52 - INFO - 测试批量处理 5 个配置文件
2025-07-28 11:35:52 - INFO - 成功加载配置文件：config\01_total_cloud_cover.yaml
2025-07-28 11:35:52 - INFO - 成功加载配置文件：config\02_wind_u10.yaml
2025-07-28 11:35:52 - INFO - 成功加载配置文件：config\03_wind_v10.yaml
2025-07-28 11:35:52 - INFO - 成功加载配置文件：config\04_mean_sea_level.yaml
2025-07-28 11:35:52 - INFO - 成功加载配置文件：config\05_total_precipitation.yaml
2025-07-28 11:35:52 - INFO - 处理摘要：
2025-07-28 11:35:52 - INFO -   配置文件数量：5
2025-07-28 11:35:52 - INFO -   参数数量：5
2025-07-28 11:35:52 - INFO -   预报步长数量：7
2025-07-28 11:35:52 - INFO -   参数列表：10u, 10v, msl, tcc, tp
2025-07-28 11:35:52 - INFO -   预报步长：[24, 48, 72, 96, 120, 144, 168]
2025-07-28 11:35:52 - INFO - 所有配置文件验证通过
2025-07-28 11:35:52 - INFO - ✓ 所有配置文件验证通过
2025-07-28 11:35:52 - INFO - 步长 24h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-24h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 24h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-24h-oper-fc.index
2025-07-28 11:35:52 - INFO - 步长 48h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-48h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 48h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-48h-oper-fc.index
2025-07-28 11:35:52 - INFO - 步长 72h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-72h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 72h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-72h-oper-fc.index
2025-07-28 11:35:52 - INFO - 步长 96h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-96h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 96h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-96h-oper-fc.index
2025-07-28 11:35:52 - INFO - 步长 120h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-120h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 120h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-120h-oper-fc.index
2025-07-28 11:35:52 - INFO - 步长 144h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-144h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 144h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-144h-oper-fc.index
2025-07-28 11:35:52 - INFO - 步长 168h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-168h-oper-fc.grib2
2025-07-28 11:35:52 - INFO - 步长 168h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-168h-oper-fc.index
2025-07-28 11:35:52 - INFO - ✓ 成功构建 7 个唯一URL
2025-07-28 11:35:52 - INFO - 优化效果：
2025-07-28 11:35:52 - INFO -   原方案下载次数：35
2025-07-28 11:35:52 - INFO -   新方案下载次数：7
2025-07-28 11:35:52 - INFO -   减少下载次数：28 (80.0%)
2025-07-28 11:35:52 - INFO - ============================================================
2025-07-28 11:35:52 - INFO - 测试完成 - 批量处理器设置成功
2025-07-28 11:35:52 - INFO - ============================================================
2025-07-28 11:35:52 - INFO - 如需执行实际下载，请运行: python main.py
2025-07-28 11:37:52 - INFO - 成功加载配置文件：config\01_total_cloud_cover.yaml
2025-07-28 11:37:52 - INFO - 成功加载配置文件：config\02_wind_u10.yaml
2025-07-28 11:37:52 - INFO - 成功加载配置文件：config\03_wind_v10.yaml
2025-07-28 11:37:52 - INFO - 成功加载配置文件：config\04_mean_sea_level.yaml
2025-07-28 11:37:52 - INFO - 成功加载配置文件：config\05_total_precipitation.yaml
2025-07-28 11:37:52 - INFO - 所有配置文件验证通过
2025-07-28 11:37:52 - INFO - 步长 24h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-24h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 24h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-24h-oper-fc.index
2025-07-28 11:37:52 - INFO - 步长 48h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-48h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 48h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-48h-oper-fc.index
2025-07-28 11:37:52 - INFO - 步长 72h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-72h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 72h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-72h-oper-fc.index
2025-07-28 11:37:52 - INFO - 步长 96h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-96h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 96h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-96h-oper-fc.index
2025-07-28 11:37:52 - INFO - 步长 120h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-120h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 120h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-120h-oper-fc.index
2025-07-28 11:37:52 - INFO - 步长 144h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-144h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 144h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-144h-oper-fc.index
2025-07-28 11:37:52 - INFO - 步长 168h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-168h-oper-fc.grib2
2025-07-28 11:37:52 - INFO - 步长 168h - Index URL: https://data.ecmwf.int/forecasts/20250728/12z/aifs-single/0p25/oper/20250728120000-168h-oper-fc.index
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 步骤 1：扫描./config目录并更新配置文件到当前UTC时间
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 找到 5 个配置文件
2025-07-28 15:48:11 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:11 - INFO - ✓ 已更新：01_total_cloud_cover.yaml
2025-07-28 15:48:11 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:11 - INFO - ✓ 已更新：02_wind_u10.yaml
2025-07-28 15:48:11 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:11 - INFO - ✓ 已更新：03_wind_v10.yaml
2025-07-28 15:48:11 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:11 - INFO - ✓ 已更新：04_mean_sea_level.yaml
2025-07-28 15:48:11 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:11 - INFO - ✓ 已更新：05_total_precipitation.yaml
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 步骤 2：执行批量数据下载和处理
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 开始批量处理 5 个配置文件
2025-07-28 15:48:11 - INFO - 成功加载配置文件：config\01_total_cloud_cover.yaml
2025-07-28 15:48:11 - INFO - 成功加载配置文件：config\02_wind_u10.yaml
2025-07-28 15:48:11 - INFO - 成功加载配置文件：config\03_wind_v10.yaml
2025-07-28 15:48:11 - INFO - 成功加载配置文件：config\04_mean_sea_level.yaml
2025-07-28 15:48:11 - INFO - 成功加载配置文件：config\05_total_precipitation.yaml
2025-07-28 15:48:11 - INFO - 处理摘要：
2025-07-28 15:48:11 - INFO -   配置文件数量：5
2025-07-28 15:48:11 - INFO -   参数数量：5
2025-07-28 15:48:11 - INFO -   预报步长数量：7
2025-07-28 15:48:11 - INFO -   参数列表：10u, 10v, msl, tcc, tp
2025-07-28 15:48:11 - INFO -   预报步长：[24, 48, 72, 96, 120, 144, 168]
2025-07-28 15:48:11 - ERROR - 配置文件 config\02_wind_u10.yaml 的数据源或日期与其他文件不一致
2025-07-28 15:48:11 - ERROR - 配置文件验证失败
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 步骤 3：清理临时grib2文件
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 步骤 4：总结
2025-07-28 15:48:11 - INFO - ============================================================
2025-07-28 15:48:11 - INFO - 处理完成！总共 5 个配置文件
2025-07-28 15:48:11 - INFO - 成功：0 个
2025-07-28 15:48:11 - INFO - 失败：5 个
2025-07-28 15:48:11 - INFO - 失败的配置文件：
2025-07-28 15:48:11 - INFO -   ✗ 01_total_cloud_cover.yaml
2025-07-28 15:48:11 - INFO -   ✗ 02_wind_u10.yaml
2025-07-28 15:48:11 - INFO -   ✗ 03_wind_v10.yaml
2025-07-28 15:48:11 - INFO -   ✗ 04_mean_sea_level.yaml
2025-07-28 15:48:11 - INFO -   ✗ 05_total_precipitation.yaml
2025-07-28 15:48:57 - INFO - ============================================================
2025-07-28 15:48:57 - INFO - 步骤 1：扫描./config目录并更新配置文件到当前UTC时间
2025-07-28 15:48:57 - INFO - ============================================================
2025-07-28 15:48:57 - INFO - 找到 5 个配置文件
2025-07-28 15:48:57 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:57 - INFO - ✓ 已更新：01_total_cloud_cover.yaml
2025-07-28 15:48:57 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:57 - INFO - ✓ 已更新：02_wind_u10.yaml
2025-07-28 15:48:57 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:57 - INFO - ✓ 已更新：03_wind_v10.yaml
2025-07-28 15:48:57 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:57 - INFO - ✓ 已更新：04_mean_sea_level.yaml
2025-07-28 15:48:57 - INFO - 更新配置：下载日期=20250728，输出目录=/data2/cloud/2025_07_28
2025-07-28 15:48:57 - INFO - ✓ 已更新：05_total_precipitation.yaml
2025-07-28 15:48:57 - INFO - ============================================================
2025-07-28 15:48:57 - INFO - 步骤 2：执行批量数据下载和处理
2025-07-28 15:48:57 - INFO - ============================================================
2025-07-28 15:48:57 - INFO - 开始批量处理 5 个配置文件
2025-07-28 15:48:57 - INFO - 成功加载配置文件：config\01_total_cloud_cover.yaml
2025-07-28 15:48:57 - INFO - 成功加载配置文件：config\02_wind_u10.yaml
2025-07-28 15:48:57 - INFO - 成功加载配置文件：config\03_wind_v10.yaml
2025-07-28 15:48:57 - INFO - 成功加载配置文件：config\04_mean_sea_level.yaml
2025-07-28 15:48:57 - INFO - 成功加载配置文件：config\05_total_precipitation.yaml
2025-07-28 15:48:57 - INFO - 处理摘要：
2025-07-28 15:48:57 - INFO -   配置文件数量：5
2025-07-28 15:48:57 - INFO -   参数数量：5
2025-07-28 15:48:57 - INFO -   预报步长数量：7
2025-07-28 15:48:57 - INFO -   参数列表：10u, 10v, msl, tcc, tp
2025-07-28 15:48:57 - INFO -   预报步长：[24, 48, 72, 96, 120, 144, 168]
2025-07-28 15:48:57 - INFO - 所有配置文件验证通过
2025-07-28 15:48:57 - INFO - 步长 24h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-24h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 24h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-24h-oper-fc.index
2025-07-28 15:48:57 - INFO - 步长 48h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-48h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 48h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-48h-oper-fc.index
2025-07-28 15:48:57 - INFO - 步长 72h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-72h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 72h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-72h-oper-fc.index
2025-07-28 15:48:57 - INFO - 步长 96h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-96h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 96h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-96h-oper-fc.index
2025-07-28 15:48:57 - INFO - 步长 120h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-120h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 120h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-120h-oper-fc.index
2025-07-28 15:48:57 - INFO - 步长 144h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-144h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 144h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-144h-oper-fc.index
2025-07-28 15:48:57 - INFO - 步长 168h - GRIB2 URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-168h-oper-fc.grib2
2025-07-28 15:48:57 - INFO - 步长 168h - Index URL: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-168h-oper-fc.index
2025-07-28 15:48:57 - INFO - ============================================================
2025-07-28 15:48:57 - INFO - 步骤 2.1：统一下载所有index文件
2025-07-28 15:48:57 - INFO - ============================================================
2025-07-28 15:48:57 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-24h-oper-fc.index
2025-07-28 15:48:59 - INFO - 成功下载index文件，大小：24678 字符
2025-07-28 15:48:59 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:48:59 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-48h-oper-fc.index
2025-07-28 15:49:00 - INFO - 成功下载index文件，大小：24680 字符
2025-07-28 15:49:00 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:49:00 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-72h-oper-fc.index
2025-07-28 15:49:02 - INFO - 成功下载index文件，大小：24676 字符
2025-07-28 15:49:02 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:49:02 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-96h-oper-fc.index
2025-07-28 15:49:03 - WARNING - 第1次重试失败：HTTPSConnectionPool(host='data.ecmwf.int', port=443): Max retries exceeded with url: /forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-96h-oper-fc.index (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))
2025-07-28 15:49:03 - INFO - 等待1.0秒后重试...
2025-07-28 15:49:04 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-96h-oper-fc.index
2025-07-28 15:49:06 - INFO - 成功下载index文件，大小：24670 字符
2025-07-28 15:49:06 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:49:06 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-120h-oper-fc.index
2025-07-28 15:49:07 - INFO - 成功下载index文件，大小：24770 字符
2025-07-28 15:49:07 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:49:07 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-144h-oper-fc.index
2025-07-28 15:49:09 - INFO - 成功下载index文件，大小：24775 字符
2025-07-28 15:49:09 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:49:09 - INFO - 开始下载index文件: https://data.ecmwf.int/forecasts/20250728/00z/aifs-single/0p25/oper/20250728000000-168h-oper-fc.index
2025-07-28 15:49:11 - INFO - 成功下载index文件，大小：24773 字符
2025-07-28 15:49:11 - INFO - 成功解析index文件，共102条记录
2025-07-28 15:49:11 - INFO - 成功下载 7 个index文件
2025-07-28 15:49:11 - INFO - ============================================================
2025-07-28 15:49:11 - INFO - 步骤 2.2：从index文件中收集所有参数的位置信息
2025-07-28 15:49:11 - INFO - ============================================================
2025-07-28 15:49:11 - INFO - 收集参数 tcc 的位置信息
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 24h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 48h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 72h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 96h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 120h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 144h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tcc 数据
2025-07-28 15:49:11 - INFO -   步长 168h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 参数 tcc 共找到 7 个有效步长
2025-07-28 15:49:11 - INFO - 收集参数 10u 的位置信息
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 24h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 48h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 72h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 96h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 120h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 144h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10u 数据
2025-07-28 15:49:11 - INFO -   步长 168h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 参数 10u 共找到 7 个有效步长
2025-07-28 15:49:11 - INFO - 收集参数 10v 的位置信息
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 24h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 48h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 72h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 96h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 120h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 144h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 10v 数据
2025-07-28 15:49:11 - INFO -   步长 168h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 参数 10v 共找到 7 个有效步长
2025-07-28 15:49:11 - INFO - 收集参数 msl 的位置信息
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 24h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 48h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 72h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 96h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 120h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 144h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 msl 数据
2025-07-28 15:49:11 - INFO -   步长 168h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 参数 msl 共找到 7 个有效步长
2025-07-28 15:49:11 - INFO - 收集参数 tp 的位置信息
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 24h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 48h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 72h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 96h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 120h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 144h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 找到 1/1 个时间步长的 tp 数据
2025-07-28 15:49:11 - INFO -   步长 168h: 找到 1 个数据段
2025-07-28 15:49:11 - INFO - 参数 tp 共找到 7 个有效步长
2025-07-28 15:49:11 - INFO - ============================================================
2025-07-28 15:49:11 - INFO - 步骤 2.3：批量下载GRIB2数据段
2025-07-28 15:49:11 - INFO - ============================================================
2025-07-28 15:49:11 - INFO - 下载步长 24h 的数据
2025-07-28 15:49:11 - INFO - 下载步长 24 的数据
2025-07-28 15:49:11 - INFO - 开始下载范围：49196222-50520121 (1323900 bytes)
2025-07-28 15:49:14 - INFO - 成功下载 1323900 bytes
2025-07-28 15:49:14 - INFO - 下载步长 24 的数据
2025-07-28 15:49:14 - INFO - 开始下载范围：23460794-24177210 (716417 bytes)
2025-07-28 15:49:14 - INFO - 成功下载 716417 bytes
2025-07-28 15:49:14 - INFO - 下载步长 24 的数据
2025-07-28 15:49:14 - INFO - 开始下载范围：28060159-28793776 (733618 bytes)
2025-07-28 15:49:15 - INFO - 成功下载 733618 bytes
2025-07-28 15:49:15 - INFO - 下载步长 24 的数据
2025-07-28 15:49:15 - INFO - 开始下载范围：19241599-19729972 (488374 bytes)
2025-07-28 15:49:15 - INFO - 成功下载 488374 bytes
2025-07-28 15:49:15 - INFO - 下载步长 24 的数据
2025-07-28 15:49:15 - INFO - 开始下载范围：52962744-53765263 (802520 bytes)
2025-07-28 15:49:16 - INFO - 成功下载 802520 bytes
2025-07-28 15:49:16 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:16 - INFO - 下载步长 48h 的数据
2025-07-28 15:49:16 - INFO - 下载步长 48 的数据
2025-07-28 15:49:16 - INFO - 开始下载范围：46511140-47827368 (1316229 bytes)
2025-07-28 15:49:17 - INFO - 成功下载 1316229 bytes
2025-07-28 15:49:17 - INFO - 下载步长 48 的数据
2025-07-28 15:49:17 - INFO - 开始下载范围：23029338-23727969 (698632 bytes)
2025-07-28 15:49:17 - INFO - 成功下载 698632 bytes
2025-07-28 15:49:17 - INFO - 下载步长 48 的数据
2025-07-28 15:49:17 - INFO - 开始下载范围：25908653-26630007 (721355 bytes)
2025-07-28 15:49:18 - INFO - 成功下载 721355 bytes
2025-07-28 15:49:18 - INFO - 下载步长 48 的数据
2025-07-28 15:49:18 - INFO - 开始下载范围：19306267-19789445 (483179 bytes)
2025-07-28 15:49:18 - INFO - 成功下载 483179 bytes
2025-07-28 15:49:18 - INFO - 下载步长 48 的数据
2025-07-28 15:49:18 - INFO - 开始下载范围：48928858-49840464 (911607 bytes)
2025-07-28 15:49:19 - INFO - 成功下载 911607 bytes
2025-07-28 15:49:19 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:19 - INFO - 下载步长 72h 的数据
2025-07-28 15:49:19 - INFO - 下载步长 72 的数据
2025-07-28 15:49:19 - INFO - 开始下载范围：47977070-49319684 (1342615 bytes)
2025-07-28 15:49:20 - INFO - 成功下载 1342615 bytes
2025-07-28 15:49:20 - INFO - 下载步长 72 的数据
2025-07-28 15:49:20 - INFO - 开始下载范围：22841269-23535599 (694331 bytes)
2025-07-28 15:49:20 - INFO - 成功下载 694331 bytes
2025-07-28 15:49:20 - INFO - 下载步长 72 的数据
2025-07-28 15:49:20 - INFO - 开始下载范围：28260464-28978443 (717980 bytes)
2025-07-28 15:49:21 - INFO - 成功下载 717980 bytes
2025-07-28 15:49:21 - INFO - 下载步长 72 的数据
2025-07-28 15:49:21 - INFO - 开始下载范围：19155984-19641425 (485442 bytes)
2025-07-28 15:49:21 - INFO - 成功下载 485442 bytes
2025-07-28 15:49:21 - INFO - 下载步长 72 的数据
2025-07-28 15:49:21 - INFO - 开始下载范围：52165457-53022627 (857171 bytes)
2025-07-28 15:49:22 - INFO - 成功下载 857171 bytes
2025-07-28 15:49:22 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:22 - INFO - 下载步长 96h 的数据
2025-07-28 15:49:22 - INFO - 下载步长 96 的数据
2025-07-28 15:49:22 - INFO - 开始下载范围：42486139-43830054 (1343916 bytes)
2025-07-28 15:49:23 - INFO - 成功下载 1343916 bytes
2025-07-28 15:49:23 - INFO - 下载步长 96 的数据
2025-07-28 15:49:23 - INFO - 开始下载范围：20023161-20713633 (690473 bytes)
2025-07-28 15:49:23 - INFO - 成功下载 690473 bytes
2025-07-28 15:49:23 - INFO - 下载步长 96 的数据
2025-07-28 15:49:23 - INFO - 开始下载范围：23594909-24318619 (723711 bytes)
2025-07-28 15:49:24 - INFO - 成功下载 723711 bytes
2025-07-28 15:49:24 - INFO - 下载步长 96 的数据
2025-07-28 15:49:24 - INFO - 开始下载范围：15927940-16419933 (491994 bytes)
2025-07-28 15:49:25 - INFO - 成功下载 491994 bytes
2025-07-28 15:49:25 - INFO - 下载步长 96 的数据
2025-07-28 15:49:25 - INFO - 开始下载范围：46360128-47264244 (904117 bytes)
2025-07-28 15:49:25 - INFO - 成功下载 904117 bytes
2025-07-28 15:49:25 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:25 - INFO - 下载步长 120h 的数据
2025-07-28 15:49:25 - INFO - 下载步长 120 的数据
2025-07-28 15:49:25 - INFO - 开始下载范围：46278918-47617737 (1338820 bytes)
2025-07-28 15:49:26 - INFO - 成功下载 1338820 bytes
2025-07-28 15:49:26 - INFO - 下载步长 120 的数据
2025-07-28 15:49:26 - INFO - 开始下载范围：22653237-23346141 (692905 bytes)
2025-07-28 15:49:27 - INFO - 成功下载 692905 bytes
2025-07-28 15:49:27 - INFO - 下载步长 120 的数据
2025-07-28 15:49:27 - INFO - 开始下载范围：25502160-26227589 (725430 bytes)
2025-07-28 15:49:27 - INFO - 成功下载 725430 bytes
2025-07-28 15:49:27 - INFO - 下载步长 120 的数据
2025-07-28 15:49:27 - INFO - 开始下载范围：19068734-19563669 (494936 bytes)
2025-07-28 15:49:28 - INFO - 成功下载 494936 bytes
2025-07-28 15:49:28 - INFO - 下载步长 120 的数据
2025-07-28 15:49:28 - INFO - 开始下载范围：49649281-50589999 (940719 bytes)
2025-07-28 15:49:28 - INFO - 成功下载 940719 bytes
2025-07-28 15:49:28 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:28 - INFO - 下载步长 144h 的数据
2025-07-28 15:49:28 - INFO - 下载步长 144 的数据
2025-07-28 15:49:28 - INFO - 开始下载范围：50434474-51749970 (1315497 bytes)
2025-07-28 15:49:29 - INFO - 成功下载 1315497 bytes
2025-07-28 15:49:29 - INFO - 下载步长 144 的数据
2025-07-28 15:49:29 - INFO - 开始下载范围：22804204-23495132 (690929 bytes)
2025-07-28 15:49:30 - INFO - 成功下载 690929 bytes
2025-07-28 15:49:30 - INFO - 下载步长 144 的数据
2025-07-28 15:49:30 - INFO - 开始下载范围：28721830-29443590 (721761 bytes)
2025-07-28 15:49:30 - INFO - 成功下载 721761 bytes
2025-07-28 15:49:30 - INFO - 下载步长 144 的数据
2025-07-28 15:49:30 - INFO - 开始下载范围：19852950-20345511 (492562 bytes)
2025-07-28 15:49:31 - INFO - 成功下载 492562 bytes
2025-07-28 15:49:31 - INFO - 下载步长 144 的数据
2025-07-28 15:49:31 - INFO - 开始下载范围：53252077-54099513 (847437 bytes)
2025-07-28 15:49:31 - INFO - 成功下载 847437 bytes
2025-07-28 15:49:31 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:31 - INFO - 下载步长 168h 的数据
2025-07-28 15:49:31 - INFO - 下载步长 168 的数据
2025-07-28 15:49:31 - INFO - 开始下载范围：46314045-47643974 (1329930 bytes)
2025-07-28 15:49:32 - INFO - 成功下载 1329930 bytes
2025-07-28 15:49:32 - INFO - 下载步长 168 的数据
2025-07-28 15:49:32 - INFO - 开始下载范围：21707808-22397909 (690102 bytes)
2025-07-28 15:49:33 - INFO - 成功下载 690102 bytes
2025-07-28 15:49:33 - INFO - 下载步长 168 的数据
2025-07-28 15:49:33 - INFO - 开始下载范围：26820829-27535568 (714740 bytes)
2025-07-28 15:49:33 - INFO - 成功下载 714740 bytes
2025-07-28 15:49:33 - INFO - 下载步长 168 的数据
2025-07-28 15:49:33 - INFO - 开始下载范围：19040475-19530647 (490173 bytes)
2025-07-28 15:49:34 - INFO - 成功下载 490173 bytes
2025-07-28 15:49:34 - INFO - 下载步长 168 的数据
2025-07-28 15:49:34 - INFO - 开始下载范围：49613169-50483395 (870227 bytes)
2025-07-28 15:49:34 - INFO - 成功下载 870227 bytes
2025-07-28 15:49:34 - INFO - 成功下载 1 个时间步长的数据
2025-07-28 15:49:34 - INFO - 批量下载完成，共处理 5 个参数
2025-07-28 15:49:34 - INFO - ============================================================
2025-07-28 15:49:34 - INFO - 步骤 2.4：处理所有参数数据并保存
2025-07-28 15:49:34 - INFO - ============================================================
2025-07-28 15:49:34 - INFO - 处理参数 tcc (01_total_cloud_cover.yaml)
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmplkg51tsf.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp5rajfp2b.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp9cvrgx3x.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpgqu35mlr.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpgpf5hhni.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpczx5zazn.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmprsjx5lyn.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 成功处理 0 个时间步长的数据
2025-07-28 15:49:34 - ERROR - 参数 tcc 的数据处理失败
2025-07-28 15:49:34 - INFO - 处理参数 10u (02_wind_u10.yaml)
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp2huxom2h.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp3yd2qynn.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpm1ta3s92.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpnlkffrhj.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpji8azd3f.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpko9sz14w.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp8w6m1nfd.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 成功处理 0 个时间步长的数据
2025-07-28 15:49:34 - ERROR - 参数 10u 的数据处理失败
2025-07-28 15:49:34 - INFO - 处理参数 10v (03_wind_v10.yaml)
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpbxihr9il.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpvqnq3kyn.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp14kgiggn.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpoxlzu3md.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpx9642pvj.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpj770fcyp.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpztd1vdzj.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 成功处理 0 个时间步长的数据
2025-07-28 15:49:34 - ERROR - 参数 10v 的数据处理失败
2025-07-28 15:49:34 - INFO - 处理参数 msl (04_mean_sea_level.yaml)
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpn5xsy4pu.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp5i0kdjqo.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpeksq4v09.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpr9ay3ktm.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpwdt0i841.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpgqwzqvsx.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpvkme640y.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 成功处理 0 个时间步长的数据
2025-07-28 15:49:34 - ERROR - 参数 msl 的数据处理失败
2025-07-28 15:49:34 - INFO - 处理参数 tp (05_total_precipitation.yaml)
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmprf4lifqw.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp09ztay4l.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp05fd1wkg.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpiih8t62z.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmp9xkr1j5a.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpr7anxtjv.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 开始加载GRIB2文件：C:\Users\<USER>\AppData\Local\Temp\tmpa93s7u9m.grib2
2025-07-28 15:49:34 - ERROR - 加载GRIB2文件失败：unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']. To install additional dependencies, see:
https://docs.xarray.dev/en/stable/user-guide/io.html 
https://docs.xarray.dev/en/stable/getting-started-guide/installing.html
2025-07-28 15:49:34 - INFO - 成功处理 0 个时间步长的数据
2025-07-28 15:49:34 - ERROR - 参数 tp 的数据处理失败
2025-07-28 15:49:34 - INFO - ============================================================
2025-07-28 15:49:34 - INFO - 步骤 3：清理临时grib2文件
2025-07-28 15:49:34 - INFO - ============================================================
2025-07-28 15:49:34 - INFO - ============================================================
2025-07-28 15:49:34 - INFO - 步骤 4：总结
2025-07-28 15:49:34 - INFO - ============================================================
2025-07-28 15:49:34 - INFO - 处理完成！总共 5 个配置文件
2025-07-28 15:49:34 - INFO - 成功：0 个
2025-07-28 15:49:34 - INFO - 失败：5 个
2025-07-28 15:49:34 - INFO - 失败的配置文件：
2025-07-28 15:49:34 - INFO -   ✗ 01_total_cloud_cover.yaml
2025-07-28 15:49:34 - INFO -   ✗ 02_wind_u10.yaml
2025-07-28 15:49:34 - INFO -   ✗ 03_wind_v10.yaml
2025-07-28 15:49:34 - INFO -   ✗ 04_mean_sea_level.yaml
2025-07-28 15:49:34 - INFO -   ✗ 05_total_precipitation.yaml
