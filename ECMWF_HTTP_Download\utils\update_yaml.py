"""
YAML配置文件日期更新模块
自动更新配置文件中的日期到当前UTC时间
"""

import yaml
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Optional

from .logger import logger


class YamlUpdater:
    """YAML配置文件日期更新器"""
    
    def __init__(self, config_path: str):
        """
        初始化更新器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config_data = None
        
        if not self.config_path.exists():
            logger.error(f"配置文件不存在：{config_path}")
            raise FileNotFoundError(f"配置文件不存在：{config_path}")
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
            return self.config_data
        except Exception as e:
            logger.error(f"加载配置文件失败：{e}")
            raise
    
    def get_current_dates(self) -> tuple:
        """获取当前UTC和北京时间日期"""
        utc_date = datetime.utcnow().strftime('%Y%m%d')
        # 北京时间比UTC快8小时
        beijing_time = datetime.utcnow() + timedelta(hours=8)
        beijing_date = beijing_time.strftime('%Y%m%d')
        return utc_date, beijing_date
    
    def update_to_current_utc(self) -> bool:
        """
        将配置文件中的日期更新到当前UTC时间，但输出目录使用北京时间
        
        Returns:
            更新是否成功
        """
        try:
            # 1. 加载当前配置
            self.load_config()
            
            # 2. 获取当前日期
            current_utc_date, current_beijing_date = self.get_current_dates()
            
            # 3. 更新配置中的日期（使用UTC时间用于下载）
            if 'date_config' not in self.config_data:
                self.config_data['date_config'] = {}
            
            old_date = self.config_data['date_config'].get('date', 'unknown')
            self.config_data['date_config']['date'] = current_utc_date
            
            # 4. 更新输出目录（使用北京时间）
            if 'output' not in self.config_data:
                self.config_data['output'] = {}
            
            # 根据北京时间生成输出路径
            dt = datetime.strptime(current_beijing_date, '%Y%m%d')
            output_path = f"/data2/cloud/{dt.year}_{dt.month:02d}_{dt.day:02d}"
            self.config_data['output']['directory'] = output_path
            
            logger.info(f"更新配置：下载日期={current_utc_date}，输出目录={output_path}")
            
            # 5. 保存配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    self.config_data, 
                    f, 
                    default_flow_style=False,
                    allow_unicode=True,
                    sort_keys=False
                )
            
            return True
            
        except Exception as e:
            logger.error(f"更新配置文件失败：{e}")
            return False 