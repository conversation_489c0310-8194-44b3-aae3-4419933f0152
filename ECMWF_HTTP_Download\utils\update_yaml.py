"""
YAML配置文件日期更新模块
自动更新配置文件中的日期到当前UTC时间
"""

import yaml
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Optional

from .logger import logger


class YamlUpdater:
    """YAML配置文件日期更新器"""
    
    def __init__(self, config_path: str):
        """
        初始化更新器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config_data = None
        
        if not self.config_path.exists():
            logger.error(f"配置文件不存在：{config_path}")
            raise FileNotFoundError(f"配置文件不存在：{config_path}")
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
            return self.config_data
        except Exception as e:
            logger.error(f"加载配置文件失败：{e}")
            raise
    
    def get_current_dates(self) -> tuple:
        """获取当前UTC和北京时间日期"""
        utc_date = datetime.utcnow().strftime('%Y%m%d')
        # 北京时间比UTC快8小时
        beijing_time = datetime.utcnow() + timedelta(hours=8)
        beijing_date = beijing_time.strftime('%Y%m%d')
        return utc_date, beijing_date
    
    def update_to_current_utc(self) -> bool:
        """
        将配置文件中的日期更新到当前UTC时间，输出目录只更新日期部分
        
        Returns:
            更新是否成功
        """
        try:
            # 1. 加载当前配置
            self.load_config()
            
            # 2. 获取当前日期
            current_utc_date, current_beijing_date = self.get_current_dates()
            
            # 3. 更新配置中的日期（使用UTC时间用于下载）
            if 'date_config' not in self.config_data:
                self.config_data['date_config'] = {}
            
            old_date = self.config_data['date_config'].get('date', 'unknown')
            self.config_data['date_config']['date'] = current_utc_date
            
            # 4. 更新输出目录（读取现有目录，只更新日期部分）
            if 'output' not in self.config_data:
                self.config_data['output'] = {}
            
            # 获取现有输出目录
            current_output_dir = self.config_data['output'].get('directory', '')
            
            if current_output_dir:
                # 解析现有路径，只更新日期部分
                import re
                # 匹配日期部分（如 2025_07_25）
                date_pattern = r'\d{4}_\d{2}_\d{2}'
                
                if re.search(date_pattern, current_output_dir):
                    # 根据北京时间生成新的日期格式
                    dt = datetime.strptime(current_beijing_date, '%Y%m%d')
                    new_date_str = f"{dt.year}_{dt.month:02d}_{dt.day:02d}"
                    
                    # 替换日期部分
                    new_output_path = re.sub(date_pattern, new_date_str, current_output_dir)
                    self.config_data['output']['directory'] = new_output_path
                    
                    logger.info(f"更新配置：下载日期={current_utc_date}，输出目录={new_output_path}")
                else:
                    logger.warning(f"输出目录格式不包含日期模式，保持原有路径：{current_output_dir}")
            else:
                logger.warning("配置中未找到输出目录，跳过目录更新")
            
            # 5. 保存配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    self.config_data, 
                    f, 
                    default_flow_style=False,
                    allow_unicode=True,
                    sort_keys=False
                )
            
            return True
            
        except Exception as e:
            logger.error(f"更新配置文件失败：{e}")
            return False 