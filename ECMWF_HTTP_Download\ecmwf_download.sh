#!/usr/bin/env bash
# 在tmux中运行ecmwf数据下载任务，每天5:00执行(协商时间)

# 基本设置
export TZ='Asia/Shanghai'
SESSION_NAME="ecmwf_aifs"
SCRIPT_DIR="/workspaces/work/fisherycube-data-process/project_root/downloader/ECMWF_HTTP_Download"
CONDA_ENV="data"

# 检查tmux会话
tmux has-session -t $SESSION_NAME 2>/dev/null
if [ $? != 0 ]; then
    # 创建新会话并执行任务
    tmux new-session -d -s $SESSION_NAME
else
    # 会话存在，清除现有内容
    tmux send-keys -t $SESSION_NAME C-c
    tmux send-keys -t $SESSION_NAME "clear" C-m
fi

# 执行任务
tmux send-keys -t $SESSION_NAME "cd $SCRIPT_DIR && while true; do
    source ~/anaconda3/etc/profile.d/conda.sh
    conda activate "$CONDA_ENV"
    now=\$(date +%s)
    next=\$([ \$(date +%s) -lt \$(date -d '5:00' +%s) ] && date -d '5:00' +%s || date -d 'tomorrow 5:00' +%s)
    echo [\$(date '+%F %T')] 等待下次执行...
    sleep \$((next - now))
    echo [\$(date '+%F %T')] 开始下载ecmwf数据

    clashoff # 先关闭代理
  
    # 执行Python脚本
    timeout 1h python main.py > /dev/null 2>&1
    script_exit_code=\$?

    # 终止后台的超时任务（如果还在运行）
    kill \$timeout_pid 2>/dev/null || true

done" C-m

echo "已在tmux会话'$SESSION_NAME'中启动任务"
echo "使用'tmux attach -t $SESSION_NAME'查看状态" 