"""
Index文件解析模块
解析ECMWF index文件，提取指定参数的文件位置信息
"""

import json
from typing import Dict, List, Optional
from pathlib import Path

from .logger import logger


class IndexParser:
    """ECMWF Index文件解析器"""
    
    def __init__(self, index_content: str = None):
        """
        初始化解析器
        
        Args:
            index_content: index文件内容（可选）
        """
        self.index_content = index_content
        self.index_data = []
        
        if index_content:
            self.parse_content(index_content)
    
    def parse_content(self, content: str):
        """
        解析index文件内容
        
        Args:
            content: index文件内容字符串
        """
        try:
            self.index_data = []
            for line in content.strip().split('\n'):
                if line.strip():
                    data = json.loads(line)
                    self.index_data.append(data)
            
            logger.info(f"成功解析index文件，共{len(self.index_data)}条记录")
            
        except json.JSONDecodeError as e:
            logger.error(f"解析index文件失败：{e}")
            raise
        except Exception as e:
            logger.error(f"处理index文件时发生错误：{e}")
            raise
    
    def find_parameter_info(self, param_code: str, step: int, 
                          levtype: str = "sfc") -> Optional[Dict]:
        """
        查找指定参数的位置信息
        
        Args:
            param_code: 参数代码（如'tcc'）
            step: 预报时间步长
            levtype: 层次类型（默认'sfc'为地面）
            
        Returns:
            包含offset和length的字典，如果未找到则返回None
        """
        for record in self.index_data:
            if (record.get('param') == param_code and 
                record.get('step') == str(step) and
                record.get('levtype') == levtype):
                
                return {
                    'offset': record.get('_offset'),
                    'length': record.get('_length'),
                    'step': step,
                    'param': param_code,
                    'levtype': levtype
                }
        
        logger.warning(f"未找到参数 {param_code} 步长 {step} 层次 {levtype} 的记录")
        return None
    
    def get_all_steps_info(self, param_code: str, steps: List[int],
                          levtype: str = "sfc") -> List[Dict]:
        """
        获取多个时间步长的参数信息
        
        Args:
            param_code: 参数代码
            steps: 时间步长列表
            levtype: 层次类型
            
        Returns:
            包含所有找到的参数信息的列表
        """
        results = []
        
        for step in steps:
            info = self.find_parameter_info(param_code, step, levtype)
            if info:
                results.append(info)
            else:
                logger.warning(f"跳过未找到的步长 {step}")
        
        logger.info(f"找到 {len(results)}/{len(steps)} 个时间步长的 {param_code} 数据")
        return results
    
    def get_available_parameters(self) -> List[str]:
        """
        获取index文件中所有可用的参数代码
        
        Returns:
            参数代码列表
        """
        params = set()
        for record in self.index_data:
            if 'param' in record:
                params.add(record['param'])
        
        return sorted(list(params))
    
    def get_available_steps(self, param_code: str) -> List[int]:
        """
        获取指定参数的所有可用时间步长
        
        Args:
            param_code: 参数代码
            
        Returns:
            时间步长列表
        """
        steps = set()
        for record in self.index_data:
            if record.get('param') == param_code:
                try:
                    steps.add(int(record.get('step', 0)))
                except (ValueError, TypeError):
                    continue
        
        return sorted(list(steps)) 