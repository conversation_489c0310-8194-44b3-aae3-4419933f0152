# ECMWF AIFS 数据下载工具

基于 ECMWF AIFS 模型的气象数据自动下载工具，支持 HTTP Range 部分下载和 NetCDF 格式转换。

## 项目结构

```
.
├── main.py                  # 主程序入口
├── requirements.txt         # Python 依赖
├── ecmwf_download.sh       # Shell 脚本
├── config/                 # 配置文件目录
│   ├── 01_total_cloud_cover.yaml    # 总云量
│   ├── 02_wind_u10.yaml            # 10米U风速  
│   ├── 03_wind_v10.yaml            # 10米V风速
│   ├── 04_mean_sea_level.yaml      # 海平面气压
│   └── 05_total_precipitation.yaml # 总降水量
├── utils/                  # 工具模块
│   ├── batch_processor.py  # 批量数据处理器
│   ├── grib_processor.py   # GRIB2 处理
│   ├── index_parser.py     # Index 文件解析
│   ├── logger.py           # 日志管理
│   ├── range_downloader.py # HTTP Range 下载
│   ├── retry_handler.py    # 重试处理
│   └── update_yaml.py      # YAML 更新
└── log/                    # 日志目录
    └── ecmwf_YYYYMMDD.log
```

## 配置文件

配置文件位于 `config/` 目录，支持以下气象参数：

- `tcc` - 总云量
- `10u/10v` - 10米风速 
- `msl` - 海平面气压
- `tp` - 总降水量

### 配置示例

```yaml
data_source:
  base_url: https://data.ecmwf.int/forecasts
  model: aifs-single
  resolution: 0p25
  stream: oper

date_config:
  date: '20250724'
  time: '12'

forecast_steps:
  - 24
  - 48
  - 72
  - 96
  - 120
  - 144
  - 168

parameter:
  name: "total_cloud_cover"
  param_code: "tcc"
  levtype: "sfc"

output:
  directory: "/data2/ECMWF/aifs-single/2025_07_24"
  filename: "total_cloud_cover.nc"
  format: "netcdf4"
```

## 使用方法

1. **激活conda环境**：
   ```bash
   conda activate data
   ```

2. **运行主程序**：
   ```bash
   python main.py
   ```

3. **或使用Shell脚本**：
   ```bash
   ./ecmwf_download.sh
   ```

## 工作流程

1. **步骤1**：扫描config目录并更新配置文件到当前UTC时间
2. **步骤2.1**：统一下载所有index文件（按时间步长去重）
3. **步骤2.2**：从index文件中收集所有参数的位置信息
4. **步骤2.3**：批量下载GRIB2数据段
5. **步骤2.4**：处理所有参数数据并保存为NetCDF文件
6. **步骤3**：清理临时grib2文件
7. **步骤4**：输出处理总结

## 技术特点

- **批量处理**：统一下载index，避免重复请求
- **HTTP Range 请求**：只下载需要的数据段
- **自动时间管理**：UTC 时间自动更新
- **格式转换**：GRIB2 到 NetCDF
- **智能缓存**：index文件缓存机制
- **错误重试**：完善的异常处理

## 依赖库

- `requests` - HTTP 请求
- `xarray` - 多维数组处理
- `cfgrib` - GRIB2 文件读取
- `netcdf4` - NetCDF 文件支持
- `pyyaml` - YAML 配置解析 