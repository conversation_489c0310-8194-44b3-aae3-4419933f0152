"""
重试机制处理模块
提供简化的重试装饰器，专注于下载任务的重试需求
"""

import time
import requests
from typing import Callable, Any
from functools import wraps

from .logger import logger


def is_retryable_error(exception: Exception) -> bool:
    """
    判断错误是否可重试
    
    Args:
        exception: 异常对象
        
    Returns:
        是否可重试
    """
    if isinstance(exception, requests.exceptions.RequestException):
        if hasattr(exception, 'response') and exception.response is not None:
            status_code = exception.response.status_code
            
            # 404错误不重试
            if status_code == 404:
                return False
            
            # 429和5xx错误重试
            if status_code == 429 or (500 <= status_code < 600):
                return True
            
            # 其他4xx错误不重试
            if 400 <= status_code < 500:
                return False
        
        # 网络相关错误重试
        if isinstance(exception, (requests.exceptions.ConnectionError,
                                requests.exceptions.Timeout,
                                requests.exceptions.ConnectTimeout,
                                requests.exceptions.ReadTimeout)):
            return True
    
    return False


def retry_download(max_retries: int = 10, base_delay: float = 1.0) -> Callable:
    """
    下载专用的重试装饰器
    
    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                    
                except Exception as e:
                    last_exception = e
                    
                    # 如果是最后一次尝试，直接抛出异常
                    if attempt == max_retries:
                        break
                    
                    # 检查是否可重试
                    if not is_retryable_error(e):
                        logger.error(f"遇到不可重试的错误：{e}")
                        break
                    
                    # 计算延迟时间（简单的指数回退）
                    delay = base_delay * (2 ** attempt)
                    delay = min(delay, 30.0)  # 最大等待30秒
                    
                    # 记录重试信息
                    logger.warning(f"第{attempt + 1}次重试失败：{e}")
                    logger.info(f"等待{delay:.1f}秒后重试...")
                    
                    time.sleep(delay)
            
            # 所有重试都失败，抛出最后的异常
            logger.error(f"重试{max_retries}次后仍然失败")
            raise last_exception
            
        return wrapper
    return decorator 