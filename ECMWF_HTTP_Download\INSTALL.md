# 安装指南

## 环境要求

- Python 3.8+
- Conda 环境管理器

## 安装步骤

### 1. 创建并激活conda环境

```bash
conda create -n data python=3.12
conda activate data
```

### 2. 安装依赖包

#### 方法一：使用requirements.txt（推荐）

```bash
pip install -r requirements.txt
```

#### 方法二：手动安装

```bash
# 基础依赖
pip install requests xarray netcdf4 pyyaml

# GRIB2处理依赖（重要！）
pip install eccodes
pip install cfgrib
```

### 3. 验证安装

运行以下命令验证所有依赖是否正确安装：

```bash
python -c "
import xarray as xr
import cfgrib
import eccodes

print('✓ xarray版本:', xr.__version__)
print('✓ cfgrib版本:', cfgrib.__version__)
print('✓ eccodes版本:', eccodes.__version__)
print('✓ 支持的引擎:', list(xr.backends.list_engines().keys()))

if 'cfgrib' in xr.backends.list_engines():
    print('🎉 所有依赖安装成功！')
else:
    print('❌ cfgrib引擎未正确注册')
"
```

## 常见问题

### 问题1：cfgrib引擎未识别

**错误信息**：
```
unrecognized engine 'cfgrib' must be one of your download engines: ['netcdf4', 'store']
```

**解决方案**：
```bash
# 安装eccodes（cfgrib的核心依赖）
pip install eccodes

# 重新安装cfgrib
pip uninstall cfgrib -y
pip install cfgrib

# 验证安装
python -c "import xarray as xr; print('cfgrib' in xr.backends.list_engines())"
```

### 问题2：Windows环境下的特殊注意事项

在Windows环境下，建议使用pip而不是conda安装eccodes和cfgrib：

```bash
pip install eccodes cfgrib
```

### 问题3：版本兼容性

确保使用兼容的版本：
- `eccodes >= 2.43.0`
- `cfgrib >= 0.9.15`
- `xarray >= 2023.1.0`

## 测试安装

运行测试脚本验证功能：

```bash
python -c "
from utils.grib_processor import GribProcessor
processor = GribProcessor()
print('✓ GRIB处理器创建成功')
print('✓ 安装验证完成')
"
```

## 运行程序

安装完成后，即可运行主程序：

```bash
conda activate data
python main.py
```
