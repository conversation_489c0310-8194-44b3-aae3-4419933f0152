#!/usr/bin/env python3
"""
ECMWF数据下载主程序
自动扫描config目录中的yaml文件，更新到当前UTC时间并下载数据
"""

import glob
import os
import sys
from pathlib import Path
from typing import List, Tuple

from utils.data_merger import ECMWFDataMerger
from utils.logger import logger
from utils.update_yaml import YamlUpdater


def cleanup_temp_files():
    """清理临时grib2文件"""
    import tempfile
    temp_dir = tempfile.gettempdir()
    grib_files = glob.glob(os.path.join(temp_dir, "tmp*.grib2"))
    
    if not grib_files:
        return
        
    cleaned_count = 0
    for grib_file in grib_files:
        try:
            os.remove(grib_file)
            cleaned_count += 1
        except Exception as e:
            logger.warning(f"清理临时文件失败：{grib_file} - {e}")
    
    logger.info(f"已清理 {cleaned_count} 个临时grib2文件")


def scan_and_update_configs() -> List[str]:
    """扫描config目录中的yaml文件并更新到当前UTC时间"""
    config_dir = Path("./config")
    if not config_dir.exists():
        logger.error("config目录不存在")
        return []
    
    # 查找所有yaml文件
    yaml_files = list(config_dir.glob("*.yaml")) + list(config_dir.glob("*.yml"))
    
    if not yaml_files:
        logger.error("config目录中没有找到yaml文件")
        return []
    
    logger.info(f"找到 {len(yaml_files)} 个配置文件")
    
    updated_configs = []
    
    for yaml_file in yaml_files:
        try:
            updater = YamlUpdater(str(yaml_file))
            if updater.update_to_current_utc():
                updated_configs.append(str(yaml_file))
                logger.info(f"✓ 已更新：{yaml_file.name}")
            else:
                logger.error(f"✗ 更新失败：{yaml_file.name}")
        except Exception as e:
            logger.error(f"✗ 处理配置文件失败：{yaml_file.name} - {e}")
    
    return updated_configs


def process_configs(config_files: List[str]) -> Tuple[List[str], List[str]]:
    """处理配置文件并下载数据"""
    successful_configs = []
    failed_configs = []
    
    for config_file in config_files:
        try:
            logger.info(f"处理配置文件：{Path(config_file).name}")
            
            # 创建数据合并器
            merger = ECMWFDataMerger(config_file)
            
            # 验证配置文件
            if not merger.validate_config():
                logger.error(f"配置文件验证失败：{config_file}")
                failed_configs.append(config_file)
                continue
            
            # 执行数据处理
            success = merger.process_data()
            
            if success:
                successful_configs.append(config_file)
                logger.info(f"✓ 成功：{Path(config_file).name}")
            else:
                failed_configs.append(config_file)
                logger.error(f"✗ 失败：{Path(config_file).name}")
                
        except Exception as e:
            logger.error(f"✗ 处理配置文件异常：{Path(config_file).name} - {e}")
            failed_configs.append(config_file)
    
    return successful_configs, failed_configs


def print_summary(successful_configs: List[str], failed_configs: List[str]):
    """打印总结信息"""
    total_configs = len(successful_configs) + len(failed_configs)
    
    logger.info(f"处理完成！总共 {total_configs} 个配置文件")
    logger.info(f"成功：{len(successful_configs)} 个")
    logger.info(f"失败：{len(failed_configs)} 个")
    
    if successful_configs:
        logger.info("成功的配置文件：")
        for config in successful_configs:
            logger.info(f"  ✓ {Path(config).name}")
    
    if failed_configs:
        logger.info("失败的配置文件：")
        for config in failed_configs:
            logger.info(f"  ✗ {Path(config).name}")


def main():
    """主程序入口"""
    try:
        # 步骤1：扫描和更新配置文件
        logger.info("=" * 60)
        logger.info("步骤 1：扫描./config目录并更新配置文件到当前UTC时间")
        logger.info("=" * 60)
        
        updated_configs = scan_and_update_configs()
        
        if not updated_configs:
            logger.error("没有成功更新的配置文件，程序退出")
            sys.exit(1)
        
        # 步骤2：执行数据下载和处理
        logger.info("=" * 60)
        logger.info("步骤 2：执行数据下载和处理")
        logger.info("=" * 60)
        
        successful_configs, failed_configs = process_configs(updated_configs)
        
        # 步骤3：清理临时文件
        logger.info("=" * 60)
        logger.info("步骤 3：清理临时grib2文件")
        logger.info("=" * 60)
        
        cleanup_temp_files()
        
        # 步骤4：总结
        logger.info("=" * 60)
        logger.info("步骤 4：总结")
        logger.info("=" * 60)
        
        print_summary(successful_configs, failed_configs)
        
        # 如果所有配置都失败，退出代码为1
        if not successful_configs:
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败：{e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 