"""
HTTP Range下载模块
基于HTTP Range请求下载GRIB2文件的指定部分
"""

import requests
from typing import Dict, Optional
from pathlib import Path
import tempfile
import os

from .logger import logger
from .retry_handler import retry_download


class RangeDownloader:
    """HTTP Range请求下载器"""
    
    def __init__(self, timeout: int = 300):
        """
        初始化下载器
        
        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'ECMWF-Downloader/1.0',
            'Accept': 'application/octet-stream'
        })
    
    @retry_download(max_retries=3, base_delay=5.0)
    def download_range(self, url: str, offset: int, length: int) -> bytes:
        """
        下载指定范围的数据
        
        Args:
            url: 数据文件URL
            offset: 开始位置
            length: 数据长度
            
        Returns:
            下载的二进制数据
        """
        # 计算结束位置
        end_pos = offset + length - 1
        
        # 设置Range头
        headers = {
            'Range': f'bytes={offset}-{end_pos}'
        }
        
        logger.info(f"开始下载范围：{offset}-{end_pos} ({length} bytes)")
        
        response = self.session.get(
            url, 
            headers=headers, 
            timeout=self.timeout,
            stream=True
        )
        
        # 检查响应状态
        if response.status_code == 206:  # Partial Content
            data = response.content
            if len(data) != length:
                logger.warning(f"下载数据长度不匹配：期望{length}，实际{len(data)}")
            
            logger.info(f"成功下载 {len(data)} bytes")
            return data
            
        elif response.status_code == 200:
            # 服务器不支持Range请求，返回完整文件
            logger.warning("服务器不支持Range请求，将下载完整文件")
            data = response.content[offset:offset+length]
            logger.info(f"从完整文件中提取 {len(data)} bytes")
            return data
            
        else:
            logger.error(f"下载失败，状态码：{response.status_code}")
            response.raise_for_status()
    
    def download_multiple_ranges(self, url: str, 
                                ranges_info: list) -> Dict[int, bytes]:
        """
        下载多个范围的数据
        
        Args:
            url: 数据文件URL
            ranges_info: 包含offset、length和step信息的列表
            
        Returns:
            字典，键为step，值为对应的二进制数据
        """
        results = {}
        
        for info in ranges_info:
            step = info['step']
            offset = info['offset']
            length = info['length']
            
            try:
                logger.info(f"下载步长 {step} 的数据")
                data = self.download_range(url, offset, length)
                results[step] = data
                
            except Exception as e:
                logger.error(f"下载步长 {step} 失败：{e}")
                continue
        
        logger.info(f"成功下载 {len(results)} 个时间步长的数据")
        return results
    
    def close(self):
        """关闭会话"""
        self.session.close() 