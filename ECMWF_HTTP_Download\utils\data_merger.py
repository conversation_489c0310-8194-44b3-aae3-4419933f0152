"""
数据合并模块
整合所有功能组件，完成ECMWF数据的完整处理流程
"""

import yaml
import requests
from typing import Dict, List, Optional
from pathlib import Path

from .logger import logger
from .index_parser import IndexParser
from .range_downloader import RangeDownloader
from .grib_processor import GribProcessor
from .retry_handler import retry_download


class ECMWFDataMerger:
    """ECMWF数据合并器"""
    
    def __init__(self, config_path: str):
        """
        初始化数据合并器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.downloader = RangeDownloader()
        self.processor = GribProcessor()
        
    def _load_config(self, config_path: str) -> Dict:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"成功加载配置文件：{config_path}")
            return config
            
        except Exception as e:
            logger.error(f"加载配置文件失败：{e}")
            raise
    
    def _build_urls(self) -> Dict[int, tuple]:
        """
        构建不同时间步长的GRIB2和Index文件的URL
        
        Returns:
            字典，键为时间步长，值为(grib2_url, index_url)元组
        """
        base_url = self.config['data_source']['base_url']
        date = self.config['date_config']['date']
        time = self.config['date_config']['time']
        model = self.config['data_source']['model']
        resolution = self.config['data_source']['resolution']
        stream = self.config['data_source']['stream']
        forecast_steps = self.config['forecast_steps']
        
        # 构建URL路径
        url_path = f"{base_url}/{date}/{time}z/{model}/{resolution}/{stream}"
        
        urls_dict = {}
        
        for step in forecast_steps:
            # 文件名模式
            filename_base = f"{date}{time}0000-{step}h-{stream}-fc"
            
            grib2_url = f"{url_path}/{filename_base}.grib2"
            index_url = f"{url_path}/{filename_base}.index"
            
            urls_dict[step] = (grib2_url, index_url)
            
            logger.info(f"步长 {step}h - GRIB2 URL: {grib2_url}")
            logger.info(f"步长 {step}h - Index URL: {index_url}")
        
        return urls_dict
    
    @retry_download(max_retries=3, base_delay=1.0)
    def _download_index(self, index_url: str) -> str:
        """
        下载index文件内容
        
        Args:
            index_url: Index文件URL
            
        Returns:
            Index文件内容字符串
        """
        logger.info("开始下载index文件")
        
        response = requests.get(index_url, timeout=60)
        response.raise_for_status()
        
        content = response.text
        logger.info(f"成功下载index文件，大小：{len(content)} 字符")
        
        return content
    
    def process_data(self) -> bool:
        """
        处理完整的数据流程
        
        Returns:
            处理是否成功
        """
        try:
            # 1. 构建URLs
            urls_dict = self._build_urls()
            
            param_code = self.config['parameter']['param_code']
            levtype = self.config['parameter']['levtype']
            
            all_datasets = {}
            
            # 对每个时间步长分别处理
            for step, (grib2_url, index_url) in urls_dict.items():
                logger.info(f"处理时间步长 {step}h")
                
                try:
                    # 2. 下载index文件
                    index_content = self._download_index(index_url)
                    
                    # 3. 解析index文件
                    parser = IndexParser(index_content)
                    
                    # 4. 获取指定时间步长的参数信息
                    ranges_info = parser.get_all_steps_info(
                        param_code, 
                        [step], 
                        levtype
                    )
                    
                    if not ranges_info:
                        logger.warning(f"未找到步长 {step}h 的数据范围，跳过")
                        continue
                    
                    # 5. 下载GRIB2数据段
                    grib_data_dict = self.downloader.download_multiple_ranges(
                        grib2_url, 
                        ranges_info
                    )
                    
                    if not grib_data_dict:
                        logger.warning(f"未成功下载步长 {step}h 的数据，跳过")
                        continue
                    
                    # 6. 处理GRIB2数据
                    datasets_dict = self.processor.process_multiple_steps(
                        grib_data_dict, 
                        param_code
                    )
                    
                    if datasets_dict:
                        all_datasets.update(datasets_dict)
                        logger.info(f"成功处理步长 {step}h 的数据")
                    else:
                        logger.warning(f"未能处理步长 {step}h 的数据")
                        
                except Exception as e:
                    logger.error(f"处理步长 {step}h 时发生错误：{e}")
                    continue
            
            if not all_datasets:
                logger.error("未能处理任何数据")
                return False
            
            # 7. 合并数据集
            merged_dataset = self.processor.merge_datasets(
                all_datasets, 
                param_code
            )
            
            if merged_dataset is None:
                logger.error("数据集合并失败")
                return False
            
            # 8. 保存为NetCDF文件
            output_dir = self.config['output']['directory']
            output_filename = self.config['output']['filename']
            output_path = Path(output_dir) / output_filename
            
            success = self.processor.save_to_netcdf(
                merged_dataset, 
                str(output_path), 
                param_code
            )
            
            if success:
                logger.info(f"数据处理完成，输出文件：{output_path}")
                
                # 输出数据集信息
                info = self.processor.get_data_info(merged_dataset)
                logger.info(f"数据集信息：{info}")
                
                return True
            else:
                logger.error("保存NetCDF文件失败")
                return False
                
        except Exception as e:
            logger.error(f"数据处理过程中发生错误：{e}")
            return False
        
        finally:
            # 清理资源
            self.downloader.close()
    
    def get_available_data_info(self) -> Dict:
        """
        获取可用数据信息（不下载数据）
        
        Returns:
            可用数据信息字典
        """
        try:
            # 构建URLs
            urls_dict = self._build_urls()
            
            param_code = self.config['parameter']['param_code']
            
            all_available_params = set()
            all_available_steps = set()
            
            # 检查每个时间步长的数据
            for step, (grib2_url, index_url) in urls_dict.items():
                try:
                    # 下载index文件
                    index_content = self._download_index(index_url)
                    
                    # 解析index文件
                    parser = IndexParser(index_content)
                    
                    # 获取信息
                    available_params = parser.get_available_parameters()
                    available_steps = parser.get_available_steps(param_code)
                    
                    all_available_params.update(available_params)
                    all_available_steps.update(available_steps)
                    
                    if param_code in available_params:
                        logger.info(f"步长 {step}h 包含参数 {param_code}")
                    else:
                        logger.warning(f"步长 {step}h 不包含参数 {param_code}")
                        
                except Exception as e:
                    logger.warning(f"检查步长 {step}h 数据时发生错误：{e}")
                    continue
            
            info = {
                'available_parameters': sorted(list(all_available_params)),
                'available_steps': sorted(list(all_available_steps)),
                'requested_steps': self.config['forecast_steps'],
                'parameter_code': param_code,
                'level_type': self.config['parameter']['levtype']
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据信息失败：{e}")
            return {}
    
    def validate_config(self) -> bool:
        """
        验证配置文件的有效性
        
        Returns:
            配置是否有效
        """
        try:
            required_keys = [
                'parameter', 'data_source', 'forecast_steps', 
                'output', 'date_config'
            ]
            
            for key in required_keys:
                if key not in self.config:
                    logger.error(f"配置文件缺少必需键：{key}")
                    return False
            
            # 验证参数配置
            param_config = self.config['parameter']
            if not all(k in param_config for k in ['param_code', 'levtype']):
                logger.error("parameter配置不完整")
                return False
            
            # 验证预报步长
            if not isinstance(self.config['forecast_steps'], list):
                logger.error("forecast_steps必须是列表")
                return False
            
            # 验证输出配置
            output_config = self.config['output']
            if not all(k in output_config for k in ['directory', 'filename']):
                logger.error("output配置不完整")
                return False
            
            logger.info("配置文件验证通过")
            return True
            
        except Exception as e:
            logger.error(f"验证配置文件时发生错误：{e}")
            return False 