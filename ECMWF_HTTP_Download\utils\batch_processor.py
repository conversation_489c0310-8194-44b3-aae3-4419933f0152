"""
批量数据处理模块
整合多个配置文件，统一下载index文件，批量处理所有参数
"""

import yaml
import requests
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from collections import defaultdict

from .logger import logger
from .index_parser import IndexParser
from .range_downloader import RangeDownloader
from .grib_processor import GribProcessor
from .retry_handler import retry_download


class BatchECMWFProcessor:
    """ECMWF批量数据处理器"""
    
    def __init__(self, config_files: List[str]):
        """
        初始化批量处理器
        
        Args:
            config_files: 配置文件路径列表
        """
        self.config_files = config_files
        self.configs = {}
        self.downloader = RangeDownloader()
        self.processor = GribProcessor()
        self.index_cache = {}  # 缓存已下载的index文件
        
        # 加载所有配置文件
        self._load_all_configs()
        
    def _load_all_configs(self):
        """加载所有配置文件"""
        for config_file in self.config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.configs[config_file] = config
                logger.info(f"成功加载配置文件：{config_file}")
            except Exception as e:
                logger.error(f"加载配置文件失败：{config_file} - {e}")
                raise
    
    def _validate_all_configs(self) -> bool:
        """验证所有配置文件的一致性"""
        if not self.configs:
            logger.error("没有有效的配置文件")
            return False
        
        # 获取第一个配置作为基准
        first_config = next(iter(self.configs.values()))
        base_source = first_config['data_source']
        base_date = first_config['date_config']
        base_steps = set(first_config['forecast_steps'])
        
        # 检查所有配置的数据源和日期是否一致
        for config_file, config in self.configs.items():
            if (config['data_source'] != base_source or 
                config['date_config'] != base_date):
                logger.error(f"配置文件 {config_file} 的数据源或日期与其他文件不一致")
                return False
            
            # 检查预报步长是否一致
            if set(config['forecast_steps']) != base_steps:
                logger.warning(f"配置文件 {config_file} 的预报步长与其他文件不完全一致")
        
        logger.info("所有配置文件验证通过")
        return True
    
    def _build_unique_urls(self) -> Dict[int, Tuple[str, str]]:
        """
        构建唯一的URL集合（按时间步长去重）
        
        Returns:
            字典，键为时间步长，值为(grib2_url, index_url)元组
        """
        # 使用第一个配置文件构建URL（因为所有配置的数据源都相同）
        first_config = next(iter(self.configs.values()))
        
        base_url = first_config['data_source']['base_url']
        model = first_config['data_source']['model']
        resolution = first_config['data_source']['resolution']
        stream = first_config['data_source']['stream']
        
        date = first_config['date_config']['date']
        time = first_config['date_config']['time']
        
        # 收集所有唯一的预报步长
        all_steps = set()
        for config in self.configs.values():
            all_steps.update(config['forecast_steps'])
        
        # 构建URL路径
        url_path = f"{base_url}/{date}/{time}z/{model}/{resolution}/{stream}"
        
        urls_dict = {}
        for step in sorted(all_steps):
            filename_base = f"{date}{time}0000-{step}h-{stream}-fc"
            grib2_url = f"{url_path}/{filename_base}.grib2"
            index_url = f"{url_path}/{filename_base}.index"
            urls_dict[step] = (grib2_url, index_url)
            logger.info(f"步长 {step}h - GRIB2 URL: {grib2_url}")
            logger.info(f"步长 {step}h - Index URL: {index_url}")
        
        return urls_dict
    
    @retry_download(max_retries=3, base_delay=1.0)
    def _download_index(self, index_url: str) -> str:
        """
        下载index文件内容
        
        Args:
            index_url: Index文件URL
            
        Returns:
            Index文件内容字符串
        """
        logger.info(f"开始下载index文件: {index_url}")
        
        response = requests.get(index_url, timeout=60)
        response.raise_for_status()
        
        content = response.text
        logger.info(f"成功下载index文件，大小：{len(content)} 字符")
        
        return content
    
    def _download_all_indexes(self, urls_dict: Dict[int, Tuple[str, str]]) -> Dict[int, IndexParser]:
        """
        下载所有需要的index文件
        
        Args:
            urls_dict: URL字典
            
        Returns:
            字典，键为时间步长，值为IndexParser对象
        """
        logger.info("=" * 60)
        logger.info("步骤 2.1：统一下载所有index文件")
        logger.info("=" * 60)
        
        index_parsers = {}
        
        for step, (grib2_url, index_url) in urls_dict.items():
            try:
                # 检查缓存
                if index_url in self.index_cache:
                    logger.info(f"使用缓存的index文件：步长 {step}h")
                    index_content = self.index_cache[index_url]
                else:
                    # 下载index文件
                    index_content = self._download_index(index_url)
                    self.index_cache[index_url] = index_content
                
                # 创建解析器
                parser = IndexParser(index_content)
                index_parsers[step] = parser
                
            except Exception as e:
                logger.error(f"下载步长 {step}h 的index文件失败：{e}")
                continue
        
        logger.info(f"成功下载 {len(index_parsers)} 个index文件")
        return index_parsers

    def _collect_all_parameter_ranges(self, index_parsers: Dict[int, IndexParser]) -> Dict[str, Dict[int, List[Dict]]]:
        """
        从所有index文件中收集所有参数的范围信息

        Args:
            index_parsers: IndexParser字典

        Returns:
            嵌套字典：{config_file: {step: [range_info_list]}}
        """
        logger.info("=" * 60)
        logger.info("步骤 2.2：从index文件中收集所有参数的位置信息")
        logger.info("=" * 60)

        all_ranges = {}

        for config_file, config in self.configs.items():
            param_code = config['parameter']['param_code']
            levtype = config['parameter']['levtype']
            forecast_steps = config['forecast_steps']

            logger.info(f"收集参数 {param_code} 的位置信息")

            config_ranges = {}

            for step in forecast_steps:
                if step not in index_parsers:
                    logger.warning(f"步长 {step}h 的index文件不可用，跳过")
                    continue

                parser = index_parsers[step]
                ranges_info = parser.get_all_steps_info(param_code, [step], levtype)

                if ranges_info:
                    config_ranges[step] = ranges_info
                    logger.info(f"  步长 {step}h: 找到 {len(ranges_info)} 个数据段")
                else:
                    logger.warning(f"  步长 {step}h: 未找到参数 {param_code} 的数据")

            all_ranges[config_file] = config_ranges
            logger.info(f"参数 {param_code} 共找到 {len(config_ranges)} 个有效步长")

        return all_ranges

    def _batch_download_grib_data(self, urls_dict: Dict[int, Tuple[str, str]],
                                 all_ranges: Dict[str, Dict[int, List[Dict]]]) -> Dict[str, Dict[int, bytes]]:
        """
        批量下载所有需要的GRIB2数据段

        Args:
            urls_dict: URL字典
            all_ranges: 所有参数的范围信息

        Returns:
            嵌套字典：{config_file: {step: grib_data}}
        """
        logger.info("=" * 60)
        logger.info("步骤 2.3：批量下载GRIB2数据段")
        logger.info("=" * 60)

        all_grib_data = {}

        # 按步长组织下载任务，避免重复下载相同的数据段
        step_download_tasks = defaultdict(list)  # {step: [(config_file, ranges_info)]}

        for config_file, config_ranges in all_ranges.items():
            for step, ranges_info in config_ranges.items():
                step_download_tasks[step].append((config_file, ranges_info))

        # 按步长下载数据
        for step, tasks in step_download_tasks.items():
            if step not in urls_dict:
                logger.warning(f"步长 {step}h 的URL不可用，跳过")
                continue

            grib2_url, _ = urls_dict[step]
            logger.info(f"下载步长 {step}h 的数据")

            # 收集这个步长所有需要的范围
            all_ranges_for_step = []
            config_range_mapping = {}  # 记录每个范围属于哪个配置文件

            for config_file, ranges_info in tasks:
                for range_info in ranges_info:
                    all_ranges_for_step.append(range_info)
                    # 使用offset作为唯一标识
                    range_key = f"{range_info['offset']}-{range_info['length']}"
                    if range_key not in config_range_mapping:
                        config_range_mapping[range_key] = []
                    config_range_mapping[range_key].append(config_file)

            try:
                # 批量下载这个步长的所有数据段
                grib_data_dict = self.downloader.download_multiple_ranges(
                    grib2_url, all_ranges_for_step
                )

                # 将下载的数据分配给对应的配置文件
                for config_file, ranges_info in tasks:
                    if config_file not in all_grib_data:
                        all_grib_data[config_file] = {}

                    # 找到这个配置文件对应的数据
                    for range_info in ranges_info:
                        range_step = range_info['step']
                        if range_step in grib_data_dict:
                            all_grib_data[config_file][range_step] = grib_data_dict[range_step]
                            break

            except Exception as e:
                logger.error(f"下载步长 {step}h 的数据失败：{e}")
                continue

        logger.info(f"批量下载完成，共处理 {len(all_grib_data)} 个参数")
        return all_grib_data

    def _process_all_parameters(self, all_grib_data: Dict[str, Dict[int, bytes]]) -> Tuple[List[str], List[str]]:
        """
        处理所有参数的数据并保存为NetCDF文件

        Args:
            all_grib_data: 所有参数的GRIB数据

        Returns:
            (成功的配置文件列表, 失败的配置文件列表)
        """
        logger.info("=" * 60)
        logger.info("步骤 2.4：处理所有参数数据并保存")
        logger.info("=" * 60)

        successful_configs = []
        failed_configs = []

        for config_file, grib_data_dict in all_grib_data.items():
            try:
                config = self.configs[config_file]
                param_code = config['parameter']['param_code']

                logger.info(f"处理参数 {param_code} ({Path(config_file).name})")

                if not grib_data_dict:
                    logger.warning(f"参数 {param_code} 没有可用的数据")
                    failed_configs.append(config_file)
                    continue

                # 处理GRIB2数据
                datasets_dict = self.processor.process_multiple_steps(
                    grib_data_dict, param_code
                )

                if not datasets_dict:
                    logger.error(f"参数 {param_code} 的数据处理失败")
                    failed_configs.append(config_file)
                    continue

                # 合并数据集
                merged_dataset = self.processor.merge_datasets(
                    datasets_dict, param_code
                )

                if merged_dataset is None:
                    logger.error(f"参数 {param_code} 的数据集合并失败")
                    failed_configs.append(config_file)
                    continue

                # 保存为NetCDF文件
                output_dir = config['output']['directory']
                output_filename = config['output']['filename']
                output_path = Path(output_dir) / output_filename

                # 确保输出目录存在
                output_path.parent.mkdir(parents=True, exist_ok=True)

                success = self.processor.save_to_netcdf(
                    merged_dataset, str(output_path), param_code
                )

                if success:
                    successful_configs.append(config_file)
                    logger.info(f"✓ 成功处理参数 {param_code}，输出文件：{output_path}")

                    # 输出数据集信息
                    info = self.processor.get_data_info(merged_dataset)
                    logger.info(f"  数据集信息：{info}")
                else:
                    failed_configs.append(config_file)
                    logger.error(f"✗ 参数 {param_code} 保存NetCDF文件失败")

            except Exception as e:
                logger.error(f"✗ 处理参数配置文件异常：{Path(config_file).name} - {e}")
                failed_configs.append(config_file)

        return successful_configs, failed_configs

    def process_all_data(self) -> Tuple[List[str], List[str]]:
        """
        执行完整的批量数据处理流程

        Returns:
            (成功的配置文件列表, 失败的配置文件列表)
        """
        try:
            # 1. 验证所有配置文件
            if not self._validate_all_configs():
                logger.error("配置文件验证失败")
                return [], list(self.configs.keys())

            # 2. 构建唯一的URL集合
            urls_dict = self._build_unique_urls()
            if not urls_dict:
                logger.error("无法构建有效的URL")
                return [], list(self.configs.keys())

            # 3. 下载所有index文件
            index_parsers = self._download_all_indexes(urls_dict)
            if not index_parsers:
                logger.error("无法下载任何index文件")
                return [], list(self.configs.keys())

            # 4. 收集所有参数的范围信息
            all_ranges = self._collect_all_parameter_ranges(index_parsers)
            if not all_ranges:
                logger.error("无法收集任何参数的范围信息")
                return [], list(self.configs.keys())

            # 5. 批量下载GRIB2数据
            all_grib_data = self._batch_download_grib_data(urls_dict, all_ranges)
            if not all_grib_data:
                logger.error("无法下载任何GRIB2数据")
                return [], list(self.configs.keys())

            # 6. 处理所有参数并保存
            successful_configs, failed_configs = self._process_all_parameters(all_grib_data)

            return successful_configs, failed_configs

        except Exception as e:
            logger.error(f"批量数据处理过程中发生错误：{e}")
            return [], list(self.configs.keys())
        finally:
            # 清理资源
            self.downloader.close()

    def get_processing_summary(self) -> Dict:
        """
        获取处理摘要信息

        Returns:
            包含处理统计信息的字典
        """
        total_configs = len(self.configs)
        total_steps = set()
        total_params = set()

        for config in self.configs.values():
            total_steps.update(config['forecast_steps'])
            total_params.add(config['parameter']['param_code'])

        return {
            'total_configs': total_configs,
            'total_parameters': len(total_params),
            'total_forecast_steps': len(total_steps),
            'parameters': sorted(list(total_params)),
            'forecast_steps': sorted(list(total_steps)),
            'index_cache_size': len(self.index_cache)
        }
